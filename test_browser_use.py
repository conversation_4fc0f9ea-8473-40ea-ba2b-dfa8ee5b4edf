#!/usr/bin/env python3
"""
Test Browser-Use Basic Functionality
"""

import asyncio
from browser_use import Agent
from langchain_ollama import ChatOllama

async def test_basic_browser_use():
    """Test basic browser-use functionality"""
    print("🧪 Testing Browser-Use Basic Functionality")
    print("=" * 50)
    
    try:
        # Setup LLM
        llm = ChatOllama(model="qwen2.5:7b")
        print("✅ LLM initialized")
        
        # Create simple agent
        agent = Agent(
            task="Go to google.com and search for 'hello world'. Tell me what you see.",
            llm=llm
        )
        print("✅ Agent created")
        
        print("🚀 Running agent...")
        result = await agent.run()
        
        print("\n📋 Result:")
        print("=" * 30)
        print(result)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_basic_browser_use())
